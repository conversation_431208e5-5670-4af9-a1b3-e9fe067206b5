/**
 * Simple PDF Export Component
 * Uses html2pdf.js for direct HTML to PDF conversion with single button press
 */

'use client';

import React, { useRef } from 'react';
import dynamic from 'next/dynamic';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface SimplePdfExportProps {
  markdown: string;
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

export const SimplePdfExport: React.FC<SimplePdfExportProps> = ({
  markdown,
  filename = 'document.pdf',
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const previewRef = useRef<HTMLDivElement>(null);

  const exportToPDF = async () => {
    if (!previewRef.current) return;

    try {
      onExportStart?.();

      // Dynamically import html2pdf.js
      const html2pdf = (await import('html2pdf.js')).default;

      const element = previewRef.current;

      const options = {
        margin: 0.5,
        filename: filename,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait'
        },
      };

      await html2pdf().set(options).from(element).save();
      onExportComplete?.();
    } catch (error) {
      console.error('PDF export failed:', error);
      onExportError?.(error as Error);
    }
  };

  return (
    <div className="simple-pdf-export">
      {/* Export Button */}
      <button
        onClick={exportToPDF}
        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200 flex items-center gap-2"
        title="Export as PDF"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Export PDF
      </button>

      {/* Hidden preview for PDF generation */}
      <div
        ref={previewRef}
        className="hidden"
        style={{
          background: '#ffffff',
          color: '#000000',
          padding: '40px',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '14px',
          lineHeight: '1.6',
          maxWidth: '210mm', // A4 width
        }}
      >
        <ReactMarkdown
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <SyntaxHighlighter
                  style={oneLight}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code 
                  className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono"
                  {...props}
                >
                  {children}
                </code>
              );
            },
            img({ src, alt, ...props }) {
              return (
                <img 
                  src={src} 
                  alt={alt} 
                  style={{ 
                    maxWidth: '100%', 
                    height: 'auto',
                    display: 'block',
                    margin: '1rem 0'
                  }} 
                  {...props}
                />
              );
            },
            table({ children, ...props }) {
              return (
                <table 
                  style={{
                    width: '100%',
                    borderCollapse: 'collapse',
                    margin: '1rem 0',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    overflow: 'hidden'
                  }}
                  {...props}
                >
                  {children}
                </table>
              );
            },
            th({ children, ...props }) {
              return (
                <th 
                  style={{
                    border: '1px solid #e5e7eb',
                    padding: '0.75rem 1rem',
                    backgroundColor: '#f8fafc',
                    fontWeight: '600',
                    textAlign: 'left',
                    borderBottom: '2px solid #e5e7eb'
                  }}
                  {...props}
                >
                  {children}
                </th>
              );
            },
            td({ children, ...props }) {
              return (
                <td 
                  style={{
                    border: '1px solid #e5e7eb',
                    padding: '0.75rem 1rem',
                    textAlign: 'left',
                    verticalAlign: 'top'
                  }}
                  {...props}
                >
                  {children}
                </td>
              );
            },
            h1({ children, ...props }) {
              return (
                <h1 
                  style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    marginTop: '2rem',
                    marginBottom: '1rem',
                    borderBottom: '2px solid #3b82f6',
                    paddingBottom: '0.5rem',
                    color: '#1f2937'
                  }}
                  {...props}
                >
                  {children}
                </h1>
              );
            },
            h2({ children, ...props }) {
              return (
                <h2 
                  style={{
                    fontSize: '1.5rem',
                    fontWeight: '600',
                    marginTop: '1.5rem',
                    marginBottom: '0.75rem',
                    borderBottom: '1px solid #d1d5db',
                    paddingBottom: '0.25rem',
                    color: '#374151'
                  }}
                  {...props}
                >
                  {children}
                </h2>
              );
            },
            h3({ children, ...props }) {
              return (
                <h3 
                  style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    marginTop: '1.25rem',
                    marginBottom: '0.5rem',
                    color: '#4b5563'
                  }}
                  {...props}
                >
                  {children}
                </h3>
              );
            },
            blockquote({ children, ...props }) {
              return (
                <blockquote 
                  style={{
                    borderLeft: '4px solid #3b82f6',
                    margin: '1.5rem 0',
                    padding: '1rem 1.5rem',
                    backgroundColor: '#f8fafc',
                    fontStyle: 'italic',
                    borderRadius: '0 0.5rem 0.5rem 0'
                  }}
                  {...props}
                >
                  {children}
                </blockquote>
              );
            },
            ul({ children, ...props }) {
              return (
                <ul 
                  style={{
                    margin: '1rem 0',
                    paddingLeft: '2rem',
                    listStyleType: 'disc'
                  }}
                  {...props}
                >
                  {children}
                </ul>
              );
            },
            ol({ children, ...props }) {
              return (
                <ol 
                  style={{
                    margin: '1rem 0',
                    paddingLeft: '2rem',
                    listStyleType: 'decimal'
                  }}
                  {...props}
                >
                  {children}
                </ol>
              );
            },
            li({ children, ...props }) {
              return (
                <li 
                  style={{
                    marginBottom: '0.5rem',
                    lineHeight: '1.6'
                  }}
                  {...props}
                >
                  {children}
                </li>
              );
            },
            p({ children, ...props }) {
              return (
                <p 
                  style={{
                    margin: '1rem 0',
                    lineHeight: '1.6'
                  }}
                  {...props}
                >
                  {children}
                </p>
              );
            }
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>
    </div>
  );
};
