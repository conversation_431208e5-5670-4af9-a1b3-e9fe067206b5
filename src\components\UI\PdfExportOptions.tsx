/**
 * PDF Export Options Component
 * Provides UI for selecting PDF export method, quality, and optimization profiles
 */

import React, { useState } from 'react';
import { PdfOptimizationService } from '@/utils/pdfOptimizationSettings';

export interface PdfExportSettings {
  profile: string;
  method: 'puppeteer' | 'playwright' | 'markdown-pdf' | 'auto';
  quality: 'high' | 'medium' | 'low';
  format: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
  orientation: 'portrait' | 'landscape';
  customMargins?: {
    top: string;
    right: string;
    bottom: string;
    left: string;
  };
}

interface PdfExportOptionsProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (settings: PdfExportSettings) => void;
  isExporting?: boolean;
}

export const PdfExportOptions: React.FC<PdfExportOptionsProps> = ({
  isOpen,
  onClose,
  onExport,
  isExporting = false,
}) => {
  const [settings, setSettings] = useState<PdfExportSettings>({
    profile: 'balanced',
    method: 'auto',
    quality: 'medium',
    format: 'A4',
    orientation: 'portrait',
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleProfileChange = (profileName: string) => {
    // Simplified profile handling
    const profileSettings = {
      highQuality: { method: 'playwright' as const, quality: 'high' as const },
      balanced: { method: 'auto' as const, quality: 'medium' as const },
      compact: { method: 'markdown-pdf' as const, quality: 'low' as const },
      printReady: { method: 'playwright' as const, quality: 'high' as const },
      mobileOptimized: { method: 'puppeteer' as const, quality: 'medium' as const },
      presentation: { method: 'playwright' as const, quality: 'high' as const, orientation: 'landscape' as const },
    };

    const profile = profileSettings[profileName];
    if (profile) {
      setSettings(prev => ({
        ...prev,
        profile: profileName,
        method: profile.method,
        quality: profile.quality,
        orientation: profile.orientation || 'portrait',
      }));
    }
  };

  const handleExport = () => {
    onExport(settings);
  };

  if (!isOpen) return null;

  const profiles = [
    { key: 'balanced', name: 'Balanced', description: 'Good quality with optimized file size', estimatedSize: '200KB - 800KB' },
    { key: 'highQuality', name: 'High Quality', description: 'Maximum quality for professional documents', estimatedSize: '500KB - 2MB' },
    { key: 'compact', name: 'Compact', description: 'Smallest file size while maintaining readability', estimatedSize: '100KB - 400KB' },
    { key: 'printReady', name: 'Print Ready', description: 'Optimized for high-quality printing', estimatedSize: '600KB - 2.5MB' },
    { key: 'mobileOptimized', name: 'Mobile Optimized', description: 'Best for mobile viewing', estimatedSize: '150KB - 600KB' },
    { key: 'presentation', name: 'Presentation', description: 'Landscape format for presentations', estimatedSize: '400KB - 1.5MB' },
  ];

  const selectedProfile = profiles.find(p => p.key === settings.profile);
  const methodInfo = PdfOptimizationService.getMethodInfo(settings.method);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              PDF Export Options
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Profile Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Optimization Profile
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {profiles.map((profile) => (
                <div
                  key={profile.key}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    settings.profile === profile.key
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                  onClick={() => handleProfileChange(profile.key)}
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {profile.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {profile.description}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                    Est. size: {profile.estimatedSize}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Selected Profile Info */}
          {selectedProfile && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                {selectedProfile.name}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {selectedProfile.description}
              </p>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Estimated file size: {selectedProfile.estimatedSize}
              </div>
            </div>
          )}

          {/* Advanced Options */}
          <div className="mb-6">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              <svg
                className={`w-4 h-4 mr-1 transition-transform ${showAdvanced ? 'rotate-90' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              Advanced Options
            </button>

            {showAdvanced && (
              <div className="mt-4 space-y-4">
                {/* Export Method */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Export Method
                  </label>
                  <select
                    value={settings.method}
                    onChange={(e) => setSettings(prev => ({ ...prev, method: e.target.value as 'puppeteer' | 'playwright' | 'markdown-pdf' | 'auto' }))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="auto">Auto (Recommended)</option>
                    <option value="playwright">Playwright (Best Quality)</option>
                    <option value="puppeteer">Puppeteer (Balanced)</option>
                    <option value="markdown-pdf">Markdown-PDF (Lightweight)</option>
                  </select>
                  {methodInfo && (
                    <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      <p>{methodInfo.description}</p>
                      <div className="mt-1">
                        <span className="text-green-600 dark:text-green-400">Pros:</span> {methodInfo.pros.join(', ')}
                      </div>
                      <div className="mt-1">
                        <span className="text-orange-600 dark:text-orange-400">Cons:</span> {methodInfo.cons.join(', ')}
                      </div>
                    </div>
                  )}
                </div>

                {/* Quality */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quality
                  </label>
                  <select
                    value={settings.quality}
                    onChange={(e) => setSettings(prev => ({ ...prev, quality: e.target.value as 'high' | 'medium' | 'low' }))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="high">High (Best quality, larger file)</option>
                    <option value="medium">Medium (Balanced)</option>
                    <option value="low">Low (Smaller file, basic quality)</option>
                  </select>
                </div>

                {/* Format and Orientation */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Page Format
                    </label>
                    <select
                      value={settings.format}
                      onChange={(e) => setSettings(prev => ({ ...prev, format: e.target.value as 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid' }))}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="A4">A4</option>
                      <option value="A3">A3</option>
                      <option value="A5">A5</option>
                      <option value="Letter">Letter</option>
                      <option value="Legal">Legal</option>
                      <option value="Tabloid">Tabloid</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Orientation
                    </label>
                    <select
                      value={settings.orientation}
                      onChange={(e) => setSettings(prev => ({ ...prev, orientation: e.target.value as 'portrait' | 'landscape' }))}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="portrait">Portrait</option>
                      <option value="landscape">Landscape</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Export Info */}
          <div className="mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <div className="flex justify-between items-center">
                <span>Export method:</span>
                <span className="font-medium">{settings.method}</span>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span>Quality:</span>
                <span className="font-medium">{settings.quality}</span>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span>Format:</span>
                <span className="font-medium">{settings.format} ({settings.orientation})</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              disabled={isExporting}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleExport}
              disabled={isExporting}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              {isExporting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Exporting...
                </>
              ) : (
                'Export PDF'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
