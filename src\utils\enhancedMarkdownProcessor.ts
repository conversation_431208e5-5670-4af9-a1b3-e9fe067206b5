/**
 * Enhanced Markdown Processor with Math, Diagrams, and Advanced Features
 */

'use client';

import { marked } from 'marked';
import DOMPurify from 'dompurify';
import katex from 'katex';
import mermaid from 'mermaid';
import hljs from 'highlight.js';

// Initialize mermaid with enhanced configuration
if (typeof window !== 'undefined') {
  // Detect theme
  const isDark = document.documentElement.classList.contains('dark') ||
                 window.matchMedia('(prefers-color-scheme: dark)').matches;

  mermaid.initialize({
    startOnLoad: false,
    theme: isDark ? 'dark' : 'base',
    securityLevel: 'loose',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: 14,
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      curve: 'basis',
      padding: 20
    },
    sequence: {
      useMaxWidth: true,
      wrap: true,
      width: 150,
      height: 65,
      boxMargin: 10,
      boxTextMargin: 5,
      noteMargin: 10,
      messageMargin: 35
    },
    gantt: {
      useMaxWidth: true,
      leftPadding: 75,
      gridLineStartPadding: 35,
      fontSize: 11,
      sectionFontSize: 24,
      numberSectionStyles: 4
    },
    journey: {
      useMaxWidth: true
    },
    timeline: {
      useMaxWidth: true
    },
    gitgraph: {
      useMaxWidth: true
    },
    c4: {
      useMaxWidth: true
    },
    themeVariables: {
      primaryColor: isDark ? '#334155' : '#f8fafc',
      primaryTextColor: isDark ? '#e2e8f0' : '#1e293b',
      primaryBorderColor: isDark ? '#94a3b8' : '#334155',
      lineColor: isDark ? '#94a3b8' : '#475569',
      secondaryColor: isDark ? '#475569' : '#e2e8f0',
      tertiaryColor: isDark ? '#1e293b' : '#ffffff',
      background: isDark ? '#1e293b' : '#ffffff',
      mainBkg: isDark ? '#334155' : '#f8fafc',
      secondBkg: isDark ? '#475569' : '#e2e8f0',
      tertiaryBkg: isDark ? '#1e293b' : '#ffffff'
    }
  });
}

// Configure marked with enhanced options
marked.setOptions({
  gfm: true,
  breaks: true,
  pedantic: false,
  smartypants: true,
  tables: true,
  headerIds: true,
  mangle: false,
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.warn('Syntax highlighting failed:', err);
      }
    }
    return hljs.highlightAuto(code).value;
  }
});

/**
 * Enhanced preprocessing with math, diagrams, and advanced features
 */
function preprocessMarkdown(markdown: string): string {
  return String(markdown || '')
    // Handle math expressions (LaTeX) with improved error handling
    .replace(/\$\$([\s\S]*?)\$\$/g, (match, math) => {
      try {
        const cleanMath = math.trim();
        if (!cleanMath) {
          return '<div class="math-error">Empty math expression</div>';
        }

        const rendered = katex.renderToString(cleanMath, {
          displayMode: true,
          throwOnError: false,
          errorColor: '#dc3545',
          strict: false,
          trust: false,
          macros: {
            "\\RR": "\\mathbb{R}",
            "\\NN": "\\mathbb{N}",
            "\\ZZ": "\\mathbb{Z}",
            "\\QQ": "\\mathbb{Q}",
            "\\CC": "\\mathbb{C}"
          }
        });
        return `<div class="math-display katex-display">${rendered}</div>`;
      } catch (error) {
        console.warn('Display math rendering error:', error);
        return `<div class="math-error">Math Error: ${math.trim()}</div>`;
      }
    })
    .replace(/\$([^$\n]+?)\$/g, (match, math) => {
      try {
        const cleanMath = math.trim();
        if (!cleanMath) {
          return '<span class="math-error">Empty math expression</span>';
        }

        const rendered = katex.renderToString(cleanMath, {
          displayMode: false,
          throwOnError: false,
          errorColor: '#dc3545',
          strict: false,
          trust: false,
          macros: {
            "\\RR": "\\mathbb{R}",
            "\\NN": "\\mathbb{N}",
            "\\ZZ": "\\mathbb{Z}",
            "\\QQ": "\\mathbb{Q}",
            "\\CC": "\\mathbb{C}"
          }
        });
        return `<span class="math-inline katex-inline">${rendered}</span>`;
      } catch (error) {
        console.warn('Inline math rendering error:', error);
        return `<span class="math-error">Math Error: ${math.trim()}</span>`;
      }
    })
    
    // Handle Mermaid diagrams
    .replace(/```mermaid\n([\s\S]*?)\n```/g, (match, diagram) => {
      const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      return `<div class="mermaid-diagram" id="${id}" data-diagram="${encodeURIComponent(diagram.trim())}">${diagram.trim()}</div>`;
    })
    
    // Handle advanced containers with icons
    .replace(/^:::\s*note\s*(.*?)$/gm, '<div class="custom-container note"><div class="container-header"><span class="container-icon">📝</span><span class="container-title">$1</span></div><div class="container-content">')
    .replace(/^:::\s*warning\s*(.*?)$/gm, '<div class="custom-container warning"><div class="container-header"><span class="container-icon">⚠️</span><span class="container-title">$1</span></div><div class="container-content">')
    .replace(/^:::\s*info\s*(.*?)$/gm, '<div class="custom-container info"><div class="container-header"><span class="container-icon">ℹ️</span><span class="container-title">$1</span></div><div class="container-content">')
    .replace(/^:::\s*tip\s*(.*?)$/gm, '<div class="custom-container tip"><div class="container-header"><span class="container-icon">💡</span><span class="container-title">$1</span></div><div class="container-content">')
    .replace(/^:::\s*danger\s*(.*?)$/gm, '<div class="custom-container danger"><div class="container-header"><span class="container-icon">🚨</span><span class="container-title">$1</span></div><div class="container-content">')
    .replace(/^:::\s*success\s*(.*?)$/gm, '<div class="custom-container success"><div class="container-header"><span class="container-icon">✅</span><span class="container-title">$1</span></div><div class="container-content">')
    .replace(/^:::$/gm, '</div></div>')
    
    // Handle advanced text formatting
    .replace(/==(.*?)==/g, '<mark class="md-highlight">$1</mark>')
    .replace(/\+\+(.*?)\+\+/g, '<ins class="md-inserted">$1</ins>')
    .replace(/~([^~\s]+)~/g, '<sub class="md-subscript">$1</sub>')
    .replace(/\^([^\^\s]+)\^/g, '<sup class="md-superscript">$1</sup>')
    .replace(/\[\[([^\]]+)\]\]/g, '<kbd class="md-kbd">$1</kbd>')
    
    // Handle enhanced task lists with different states
    .replace(/^(\s*)- \[x\]/gm, '$1- <input type="checkbox" checked disabled class="task-list-checkbox task-completed">')
    .replace(/^(\s*)- \[ \]/gm, '$1- <input type="checkbox" disabled class="task-list-checkbox task-pending">')
    .replace(/^(\s*)- \[\/\]/gm, '$1- <input type="checkbox" disabled class="task-list-checkbox task-in-progress">')
    .replace(/^(\s*)- \[-\]/gm, '$1- <input type="checkbox" disabled class="task-list-checkbox task-cancelled">')
    
    // Handle footnotes
    .replace(/\[\^([^\]]+)\]/g, '<sup class="footnote-ref"><a href="#fn-$1" id="fnref-$1">$1</a></sup>')
    .replace(/^\[\^([^\]]+)\]:\s*(.+)$/gm, '<div class="footnote" id="fn-$1"><a href="#fnref-$1" class="footnote-backref">↩</a> $2</div>')
    
    // Handle definition lists
    .replace(/^([^:\n]+)\n:\s+(.+)$/gm, '<dl class="definition-list"><dt>$1</dt><dd>$2</dd></dl>')
    
    // Handle abbreviations
    .replace(/\*\[([^\]]+)\]:\s*(.+)$/gm, '<abbr title="$2">$1</abbr>')
    
    // Handle enhanced emojis and emoticons
    .replace(/:wink:/g, '😉').replace(/:cry:/g, '😢').replace(/:laughing:/g, '😆')
    .replace(/:yum:/g, '😋').replace(/:smile:/g, '😊').replace(/:heart:/g, '❤️')
    .replace(/:thumbsup:/g, '👍').replace(/:thumbsdown:/g, '👎').replace(/:fire:/g, '🔥')
    .replace(/:rocket:/g, '🚀').replace(/:star:/g, '⭐').replace(/:warning:/g, '⚠️')
    .replace(/:info:/g, 'ℹ️').replace(/:check:/g, '✅').replace(/:x:/g, '❌')
    .replace(/:tada:/g, '🎉').replace(/:sparkles:/g, '✨').replace(/:boom:/g, '💥')
    .replace(/:zap:/g, '⚡').replace(/:bulb:/g, '💡').replace(/:gear:/g, '⚙️')
    .replace(/:book:/g, '📚').replace(/:pencil:/g, '✏️').replace(/:computer:/g, '💻')
    
    // Handle emoticons
    .replace(/:-\)/g, '🙂').replace(/:-\(/g, '😞').replace(/8-\)/g, '😎')
    .replace(/;\)/g, '😉').replace(/:D/g, '😃').replace(/:P/g, '😛')
    
    // Handle enhanced tables with alignment
    .replace(/^\|(.+)\|\s*$/gm, (match, content) => {
      const cells = content.split('|').map(cell => cell.trim());
      const headerRow = cells.map(cell => `<th class="table-header-cell">${cell}</th>`).join('');
      return `<table class="enhanced-table"><thead><tr>${headerRow}</tr></thead><tbody>`;
    })
    
    // Handle progress bars
    .replace(/\[=+\s*\]/g, (match) => {
      const filled = (match.match(/=/g) || []).length;
      const total = match.length - 2; // Subtract [ and ]
      const percentage = Math.round((filled / total) * 100);
      return `<div class="progress-bar"><div class="progress-fill" style="width: ${percentage}%"></div><span class="progress-text">${percentage}%</span></div>`;
    });
}

/**
 * Enhanced code block processing with better copy functionality
 */
function enhanceCodeBlocks(html: string): string {
  return html.replace(/<pre><code(?:\s+class="([^"]*)")?>([\s\S]*?)<\/code><\/pre>/g, (match, className, code) => {
    const decodedCode = code
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // Extract language from class
    const langMatch = (className || '').match(/(?:language-|hljs\s+)(\w+)/);
    const language = langMatch ? langMatch[1] : 'text';

    // Add line numbers
    const lines = decodedCode.split('\n');
    const numberedCode = lines.map((line, index) => 
      `<span class="code-line" data-line="${index + 1}">${line}</span>`
    ).join('\n');

    return `
      <div class="enhanced-code-block">
        <div class="code-header">
          <span class="code-language">${language}</span>
          <div class="code-actions">
            <button class="copy-code-btn" data-code="${encodeURIComponent(decodedCode)}" title="Copy code">
              <span class="copy-icon">📋</span>
              <span class="copy-text">Copy</span>
            </button>
          </div>
        </div>
        <pre class="code-content ${className || `language-${language}`}"><code class="hljs">${numberedCode}</code></pre>
      </div>
    `;
  });
}

/**
 * Process Mermaid diagrams after HTML generation
 */
async function processMermaidDiagrams(container: HTMLElement): Promise<void> {
  const diagrams = container.querySelectorAll('.mermaid-diagram');

  if (diagrams.length === 0) return;

  // Reinitialize mermaid with current theme
  const isDark = document.documentElement.classList.contains('dark') ||
                 window.matchMedia('(prefers-color-scheme: dark)').matches;

  mermaid.initialize({
    startOnLoad: false,
    theme: isDark ? 'dark' : 'base',
    securityLevel: 'loose',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: 14,
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      curve: 'basis',
      padding: 20
    },
    themeVariables: {
      primaryColor: isDark ? '#334155' : '#f8fafc',
      primaryTextColor: isDark ? '#e2e8f0' : '#1e293b',
      primaryBorderColor: isDark ? '#94a3b8' : '#334155',
      lineColor: isDark ? '#94a3b8' : '#475569',
      secondaryColor: isDark ? '#475569' : '#e2e8f0',
      tertiaryColor: isDark ? '#1e293b' : '#ffffff',
      background: isDark ? '#1e293b' : '#ffffff',
      mainBkg: isDark ? '#334155' : '#f8fafc',
      secondBkg: isDark ? '#475569' : '#e2e8f0',
      tertiaryBkg: isDark ? '#1e293b' : '#ffffff'
    }
  });

  for (const diagram of diagrams) {
    try {
      const diagramData = decodeURIComponent(diagram.getAttribute('data-diagram') || '');
      const id = diagram.id;

      if (!diagramData.trim()) {
        throw new Error('Empty diagram data');
      }

      // Clear previous content
      diagram.innerHTML = '';

      // Render the diagram
      const { svg } = await mermaid.render(id, diagramData);
      diagram.innerHTML = svg;
      diagram.classList.add('mermaid-rendered');
      diagram.classList.remove('mermaid-error');

      // Add accessibility attributes
      const svgElement = diagram.querySelector('svg');
      if (svgElement) {
        svgElement.setAttribute('role', 'img');
        svgElement.setAttribute('aria-label', 'Mermaid diagram');
      }

    } catch (error) {
      console.error('Mermaid rendering error:', error);
      diagram.innerHTML = `<div class="diagram-error">
        <strong>Diagram Error:</strong><br>
        ${error instanceof Error ? error.message : 'Unknown error'}
        <details style="margin-top: 0.5rem;">
          <summary style="cursor: pointer;">Show diagram source</summary>
          <pre style="margin-top: 0.5rem; font-size: 0.8rem; background: rgba(0,0,0,0.1); padding: 0.5rem; border-radius: 4px;">${decodeURIComponent(diagram.getAttribute('data-diagram') || '')}</pre>
        </details>
      </div>`;
      diagram.classList.add('mermaid-error');
      diagram.classList.remove('mermaid-rendered');
    }
  }
}

/**
 * Enhanced HTML element processing
 */
function enhanceHtmlElements(html: string): string {
  return html
    // Enhance tables with consistent classes
    .replace(/<table>/g, '<div class="table-responsive-wrapper"><table class="markdown-table">')
    .replace(/<\/table>/g, '</table></div>')
    .replace(/<thead>/g, '<thead class="markdown-table-header">')
    .replace(/<tbody>/g, '<tbody class="markdown-table-body">')
    .replace(/<th>/g, '<th class="markdown-table-header-cell">')
    .replace(/<td>/g, '<td class="markdown-table-cell">')
    
    // Enhance lists
    .replace(/<ul>/g, '<ul class="enhanced-list list-disc">')
    .replace(/<ol>/g, '<ol class="enhanced-list list-decimal">')
    .replace(/<li>/g, '<li class="list-item">')
    
    // Enhance blockquotes
    .replace(/<blockquote>/g, '<blockquote class="enhanced-blockquote">')
    
    // Enhance links
    .replace(/<a\s+href="([^"]*)"([^>]*)>([\s\S]*?)<\/a>/g, (match, href, attrs, text) => {
      const isExternal = href.startsWith('http') || href.startsWith('//');
      const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
      const icon = isExternal ? ' <span class="external-link-icon">🔗</span>' : '';
      return `<a href="${href}"${target}${attrs} class="enhanced-link">${text}${icon}</a>`;
    })
    
    // Enhance paragraphs
    .replace(/<p>/g, '<p class="enhanced-paragraph">')
    
    // Enhance headers with anchor links
    .replace(/<h([1-6])([^>]*)>(.*?)<\/h[1-6]>/g, (match, level, attrs, text) => {
      const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
      return `<h${level}${attrs} id="${id}" class="enhanced-header header-${level}">
        <a href="#${id}" class="header-anchor" aria-hidden="true">#</a>
        ${text}
      </h${level}>`;
    });
}

/**
 * Main enhanced markdown processing function
 */
export function processEnhancedMarkdown(markdown: string): string {
  try {
    // Preprocess markdown
    const processedMarkdown = preprocessMarkdown(markdown);
    
    // Render with marked
    let html = marked.parse(processedMarkdown) as string;
    
    // Post-process HTML
    html = enhanceCodeBlocks(html);
    html = enhanceHtmlElements(html);
    
    return html;
  } catch (error) {
    console.error('Error processing enhanced markdown:', error);
    return '<p class="error">Error rendering markdown</p>';
  }
}

/**
 * Initialize enhanced features after DOM insertion
 */
export async function initializeEnhancedFeatures(container: HTMLElement): Promise<void> {
  try {
    // Process Mermaid diagrams
    await processMermaidDiagrams(container);
    
    // Add copy button functionality
    const copyButtons = container.querySelectorAll('.copy-code-btn');
    copyButtons.forEach(button => {
      button.addEventListener('click', async (e) => {
        e.preventDefault();
        const code = decodeURIComponent(button.getAttribute('data-code') || '');
        
        try {
          await navigator.clipboard.writeText(code);
          const copyText = button.querySelector('.copy-text');
          if (copyText) {
            const originalText = copyText.textContent;
            copyText.textContent = 'Copied!';
            setTimeout(() => {
              copyText.textContent = originalText;
            }, 2000);
          }
        } catch (err) {
          console.error('Failed to copy code:', err);
        }
      });
    });
    
    // Add smooth scrolling for anchor links
    const anchorLinks = container.querySelectorAll('.header-anchor');
    anchorLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(link.getAttribute('href') || '');
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });
    
  } catch (error) {
    console.error('Error initializing enhanced features:', error);
  }
}

/**
 * Convert enhanced markdown to sanitized HTML
 */
export function enhancedMarkdownToHtml(markdown: string): string {
  try {
    const html = processEnhancedMarkdown(markdown);
    
    // Sanitize with enhanced allowlist
    const cleanHtml = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'strong', 'em', 'b', 'i', 'u', 
        'del', 's', 'ins', 'mark', 'sub', 'sup', 'kbd', 'code', 'pre', 'div', 'span', 
        'section', 'article', 'aside', 'header', 'footer', 'nav', 'button', 'a', 'input',
        'ul', 'ol', 'li', 'dl', 'dt', 'dd', 'blockquote', 'hr', 'table', 'thead', 
        'tbody', 'tr', 'th', 'td', 'img', 'video', 'audio', 'source', 'abbr', 'details', 
        'summary', 'svg', 'path', 'g', 'rect', 'circle', 'ellipse', 'line', 'polyline', 
        'polygon', 'text', 'tspan', 'defs', 'marker', 'foreignObject'
      ],
      ALLOWED_ATTR: [
        'class', 'id', 'style', 'title', 'lang', 'dir', 'aria-label', 'role', 'href', 
        'rel', 'target', 'src', 'alt', 'width', 'height', 'controls', 'autoplay', 'loop', 
        'muted', 'colspan', 'rowspan', 'align', 'valign', 'type', 'checked', 'disabled',
        'data-code', 'data-lang', 'data-line', 'data-diagram', 'data-clipboard-text',
        'viewBox', 'xmlns', 'fill', 'stroke', 'stroke-width', 'd', 'x', 'y', 'cx', 'cy', 
        'r', 'rx', 'ry', 'x1', 'y1', 'x2', 'y2', 'points', 'transform'
      ],
      ALLOW_DATA_ATTR: true,
      USE_PROFILES: { html: true, svg: true, mathMl: true }
    });
    
    return String(cleanHtml);
  } catch (error) {
    console.error('Error converting enhanced markdown to HTML:', error);
    return '<p class="error">Error rendering markdown content</p>';
  }
}
