/**
 * PDF Optimization Settings
 * Provides optimized configurations for different use cases and quality levels
 */

import { UnifiedPdfOptions } from './unifiedPdfExport';

export interface OptimizationProfile {
  name: string;
  description: string;
  options: UnifiedPdfOptions;
  estimatedFileSize: string;
  recommendedFor: string[];
}

export const PDF_OPTIMIZATION_PROFILES: Record<string, OptimizationProfile> = {
  // High Quality - Best for professional documents, presentations
  highQuality: {
    name: 'High Quality',
    description: 'Maximum quality with accessibility features. Best for professional documents.',
    options: {
      method: 'playwright',
      quality: 'high',
      format: 'A4',
      orientation: 'portrait',
      margin: {
        top: '1in',
        right: '1in',
        bottom: '1in',
        left: '1in',
      },
      printBackground: true,
      scale: 1,
      preferCSSPageSize: false,
      tagged: true, // Accessibility
      outline: true, // Document outline
      displayHeaderFooter: false,
      timeout: 30000,
      enableFallback: true,
    },
    estimatedFileSize: '500KB - 2MB',
    recommendedFor: [
      'Professional documents',
      'Reports and presentations',
      'Documents requiring accessibility',
      'Print-ready documents',
    ],
  },

  // Balanced - Good quality with reasonable file size
  balanced: {
    name: 'Balanced',
    description: 'Good quality with optimized file size. Best for general use.',
    options: {
      method: 'puppeteer',
      quality: 'medium',
      format: 'A4',
      orientation: 'portrait',
      margin: {
        top: '0.75in',
        right: '0.75in',
        bottom: '0.75in',
        left: '0.75in',
      },
      printBackground: true,
      scale: 0.9,
      preferCSSPageSize: false,
      displayHeaderFooter: false,
      timeout: 20000,
      enableFallback: true,
    },
    estimatedFileSize: '200KB - 800KB',
    recommendedFor: [
      'General documents',
      'Web sharing',
      'Email attachments',
      'Quick exports',
    ],
  },

  // Compact - Smallest file size while maintaining readability
  compact: {
    name: 'Compact',
    description: 'Smallest file size while maintaining readability. Best for web sharing.',
    options: {
      method: 'markdown-pdf',
      quality: 'low',
      format: 'A4',
      orientation: 'portrait',
      border: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in',
      },
      timeout: 15000,
      enableFallback: true,
    },
    estimatedFileSize: '100KB - 400KB',
    recommendedFor: [
      'Web sharing',
      'Mobile viewing',
      'Quick previews',
      'Bandwidth-limited scenarios',
    ],
  },

  // Print Ready - Optimized for printing
  printReady: {
    name: 'Print Ready',
    description: 'Optimized for high-quality printing with proper margins.',
    options: {
      method: 'playwright',
      quality: 'high',
      format: 'A4',
      orientation: 'portrait',
      margin: {
        top: '1.25in',
        right: '1in',
        bottom: '1.25in',
        left: '1in',
      },
      printBackground: true,
      scale: 1,
      preferCSSPageSize: true,
      tagged: false, // Not needed for print
      outline: false, // Not needed for print
      displayHeaderFooter: true,
      headerTemplate: '<div style="font-size: 10px; text-align: center; width: 100%;"><span class="title"></span></div>',
      footerTemplate: '<div style="font-size: 10px; text-align: center; width: 100%;"><span class="pageNumber"></span> / <span class="totalPages"></span></div>',
      timeout: 30000,
      enableFallback: true,
    },
    estimatedFileSize: '600KB - 2.5MB',
    recommendedFor: [
      'Physical printing',
      'Archival documents',
      'Legal documents',
      'Formal reports',
    ],
  },

  // Mobile Optimized - Best for mobile viewing
  mobileOptimized: {
    name: 'Mobile Optimized',
    description: 'Optimized for mobile viewing with larger fonts and compact layout.',
    options: {
      method: 'puppeteer',
      quality: 'medium',
      format: 'A4',
      orientation: 'portrait',
      margin: {
        top: '0.5in',
        right: '0.3in',
        bottom: '0.5in',
        left: '0.3in',
      },
      printBackground: false, // Reduce file size
      scale: 0.8,
      preferCSSPageSize: false,
      displayHeaderFooter: false,
      timeout: 20000,
      enableFallback: true,
    },
    estimatedFileSize: '150KB - 600KB',
    recommendedFor: [
      'Mobile viewing',
      'Tablet reading',
      'Quick sharing',
      'Social media',
    ],
  },

  // Presentation - Landscape format for presentations
  presentation: {
    name: 'Presentation',
    description: 'Landscape format optimized for presentations and wide content.',
    options: {
      method: 'playwright',
      quality: 'high',
      format: 'A4',
      orientation: 'landscape',
      margin: {
        top: '0.75in',
        right: '1in',
        bottom: '0.75in',
        left: '1in',
      },
      printBackground: true,
      scale: 1,
      preferCSSPageSize: false,
      tagged: true,
      outline: true,
      displayHeaderFooter: false,
      timeout: 30000,
      enableFallback: true,
    },
    estimatedFileSize: '400KB - 1.5MB',
    recommendedFor: [
      'Presentations',
      'Wide tables and charts',
      'Landscape documents',
      'Technical diagrams',
    ],
  },
};

export class PdfOptimizationService {
  public static getProfile(profileName: string): OptimizationProfile | null {
    return PDF_OPTIMIZATION_PROFILES[profileName] || null;
  }

  public static getAllProfiles(): OptimizationProfile[] {
    return Object.values(PDF_OPTIMIZATION_PROFILES);
  }

  public static getProfileNames(): string[] {
    return Object.keys(PDF_OPTIMIZATION_PROFILES);
  }

  public static getRecommendedProfile(
    documentType: 'general' | 'professional' | 'web' | 'print' | 'mobile' | 'presentation'
  ): OptimizationProfile {
    switch (documentType) {
      case 'professional':
        return PDF_OPTIMIZATION_PROFILES.highQuality;
      case 'web':
        return PDF_OPTIMIZATION_PROFILES.compact;
      case 'print':
        return PDF_OPTIMIZATION_PROFILES.printReady;
      case 'mobile':
        return PDF_OPTIMIZATION_PROFILES.mobileOptimized;
      case 'presentation':
        return PDF_OPTIMIZATION_PROFILES.presentation;
      case 'general':
      default:
        return PDF_OPTIMIZATION_PROFILES.balanced;
    }
  }

  public static customizeProfile(
    baseProfile: string,
    customizations: Partial<UnifiedPdfOptions>
  ): UnifiedPdfOptions {
    const profile = this.getProfile(baseProfile);
    if (!profile) {
      throw new Error(`Profile '${baseProfile}' not found`);
    }

    return {
      ...profile.options,
      ...customizations,
    };
  }

  public static estimateFileSize(
    contentLength: number,
    options: UnifiedPdfOptions
  ): { min: number; max: number; unit: string } {
    // Base size estimation based on content length
    let baseSize = Math.max(contentLength * 0.1, 50); // Minimum 50KB

    // Adjust based on quality
    const qualityMultiplier = {
      high: 2.5,
      medium: 1.5,
      low: 1.0,
    }[options.quality || 'medium'];

    // Adjust based on method
    const methodMultiplier = {
      playwright: 1.3,
      puppeteer: 1.1,
      'markdown-pdf': 0.8,
    }[options.method || 'puppeteer'];

    // Adjust based on features
    let featureMultiplier = 1.0;
    if (options.printBackground) featureMultiplier += 0.3;
    if (options.tagged) featureMultiplier += 0.2;
    if (options.outline) featureMultiplier += 0.1;
    if (options.displayHeaderFooter) featureMultiplier += 0.1;

    const estimatedSize = baseSize * qualityMultiplier * methodMultiplier * featureMultiplier;

    return {
      min: Math.round(estimatedSize * 0.7),
      max: Math.round(estimatedSize * 1.5),
      unit: 'KB',
    };
  }

  public static getOptimizationTips(options: UnifiedPdfOptions): string[] {
    const tips: string[] = [];

    if (options.quality === 'high') {
      tips.push('Consider using "medium" quality for smaller file sizes');
    }

    if (options.printBackground) {
      tips.push('Disable background printing to reduce file size');
    }

    if (options.scale && options.scale > 0.9) {
      tips.push('Reduce scale to 0.8-0.9 for smaller files');
    }

    if (options.method === 'playwright' && !options.tagged && !options.outline) {
      tips.push('Consider using Puppeteer for better performance if accessibility features are not needed');
    }

    if (options.margin) {
      const margins = Object.values(options.margin);
      const hasLargeMargins = margins.some(margin =>
        margin && parseFloat(margin) > 1
      );
      if (hasLargeMargins) {
        tips.push('Reduce margins to fit more content per page');
      }
    }

    if (options.timeout && options.timeout > 20000) {
      tips.push('Consider reducing timeout for faster processing');
    }

    return tips;
  }

  public static getMethodInfo(method: 'puppeteer' | 'playwright' | 'markdown-pdf' | 'auto'): { name: string; description: string; pros: string[]; cons: string[] } {
    const methodInfo = {
      puppeteer: {
        name: 'Puppeteer',
        description: 'High-quality PDF generation using Chrome/Chromium',
        pros: [
          'Excellent CSS support',
          'High-quality rendering',
          'Good performance',
          'Reliable output'
        ],
        cons: [
          'Larger file sizes',
          'Requires Chrome/Chromium',
          'Higher memory usage'
        ]
      },
      playwright: {
        name: 'Playwright',
        description: 'Premium PDF generation with accessibility features',
        pros: [
          'Best quality output',
          'Accessibility features (tagged PDFs)',
          'Document outline support',
          'Advanced CSS support',
          'Cross-browser compatibility'
        ],
        cons: [
          'Largest file sizes',
          'Slower processing',
          'Higher resource usage'
        ]
      },
      'markdown-pdf': {
        name: 'Markdown-PDF',
        description: 'Direct markdown to PDF conversion',
        pros: [
          'Lightweight',
          'Fast processing',
          'Good for simple documents',
          'Text selectable'
        ],
        cons: [
          'Limited CSS support',
          'Basic styling options',
          'May have rendering issues with complex content'
        ]
      },
      auto: {
        name: 'Auto Selection',
        description: 'Automatically selects the best method based on requirements',
        pros: [
          'Optimized for each use case',
          'Fallback support',
          'Best balance of quality and performance'
        ],
        cons: [
          'Less predictable method selection',
          'May vary between exports'
        ]
      }
    };

    return methodInfo[method] || {
      name: 'Unknown',
      description: 'Unknown method',
      pros: [],
      cons: []
    };
  }
}

// Export default profiles for easy access
export const {
  highQuality,
  balanced,
  compact,
  printReady,
  mobileOptimized,
  presentation,
} = PDF_OPTIMIZATION_PROFILES;
