/**
 * Accessibility tests for color contrast and WCAG compliance
 * Ensures all color combinations meet WCAG AA standards
 */

import { describe, it, expect } from '@jest/test-globals';

// Color contrast calculation utilities
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

function getContrastRatio(color1: string, color2: string): number {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

// Color palette definitions
const lightTheme = {
  background: '#ffffff',
  foreground: '#1a1a1a',
  muted: '#6b7280',
  mutedForeground: '#4b5563',
  border: '#e5e7eb',
  primary: '#2563eb',
  primaryForeground: '#ffffff',
  secondary: '#f1f5f9',
  secondaryForeground: '#0f172a',
  destructive: '#dc2626',
  destructiveForeground: '#ffffff',
  success: '#059669',
  successForeground: '#ffffff',
  warning: '#d97706',
  warningForeground: '#ffffff',
  info: '#0891b2',
  infoForeground: '#ffffff',
};

const darkTheme = {
  background: '#0f172a',
  foreground: '#f1f5f9',
  muted: '#64748b',
  mutedForeground: '#94a3b8',
  border: '#334155',
  primary: '#3b82f6',
  primaryForeground: '#f8fafc',
  secondary: '#1e293b',
  secondaryForeground: '#f1f5f9',
  destructive: '#ef4444',
  destructiveForeground: '#f8fafc',
  success: '#10b981',
  successForeground: '#f8fafc',
  warning: '#f59e0b',
  warningForeground: '#1e293b',
  info: '#06b6d4',
  infoForeground: '#f8fafc',
};

// Code block colors
const codeColors = {
  light: {
    background: '#f8fafc',
    foreground: '#334155',
    inlineBackground: '#f1f5f9',
    inlineForeground: '#1e293b',
    keyword: '#7c3aed',
    string: '#059669',
    comment: '#6b7280',
    number: '#dc2626',
    function: '#2563eb',
    type: '#ea580c',
  },
  dark: {
    background: '#1e293b',
    foreground: '#e2e8f0',
    inlineBackground: '#334155',
    inlineForeground: '#e2e8f0',
    keyword: '#a78bfa',
    string: '#34d399',
    comment: '#9ca3af',
    number: '#f87171',
    function: '#60a5fa',
    type: '#fb923c',
  }
};

describe('Accessibility - Color Contrast', () => {
  describe('Light Theme WCAG AA Compliance', () => {
    it('should have sufficient contrast for main text', () => {
      const ratio = getContrastRatio(lightTheme.foreground, lightTheme.background);
      expect(ratio).toBeGreaterThanOrEqual(4.5); // WCAG AA standard
    });

    it('should have sufficient contrast for muted text', () => {
      const ratio = getContrastRatio(lightTheme.mutedForeground, lightTheme.background);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for primary elements', () => {
      const ratio = getContrastRatio(lightTheme.primaryForeground, lightTheme.primary);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for secondary elements', () => {
      const ratio = getContrastRatio(lightTheme.secondaryForeground, lightTheme.secondary);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for destructive elements', () => {
      const ratio = getContrastRatio(lightTheme.destructiveForeground, lightTheme.destructive);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for success elements', () => {
      const ratio = getContrastRatio(lightTheme.successForeground, lightTheme.success);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for warning elements', () => {
      const ratio = getContrastRatio(lightTheme.warningForeground, lightTheme.warning);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for info elements', () => {
      const ratio = getContrastRatio(lightTheme.infoForeground, lightTheme.info);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });
  });

  describe('Dark Theme WCAG AA Compliance', () => {
    it('should have sufficient contrast for main text', () => {
      const ratio = getContrastRatio(darkTheme.foreground, darkTheme.background);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for muted text', () => {
      const ratio = getContrastRatio(darkTheme.mutedForeground, darkTheme.background);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for primary elements', () => {
      const ratio = getContrastRatio(darkTheme.primaryForeground, darkTheme.primary);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for secondary elements', () => {
      const ratio = getContrastRatio(darkTheme.secondaryForeground, darkTheme.secondary);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for destructive elements', () => {
      const ratio = getContrastRatio(darkTheme.destructiveForeground, darkTheme.destructive);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for success elements', () => {
      const ratio = getContrastRatio(darkTheme.successForeground, darkTheme.success);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for warning elements', () => {
      const ratio = getContrastRatio(darkTheme.warningForeground, darkTheme.warning);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for info elements', () => {
      const ratio = getContrastRatio(darkTheme.infoForeground, darkTheme.info);
      expect(ratio).toBeGreaterThanOrEqual(4.5);
    });
  });

  describe('Code Block Color Contrast', () => {
    it('should have sufficient contrast for light theme code blocks', () => {
      const bgRatio = getContrastRatio(codeColors.light.foreground, codeColors.light.background);
      expect(bgRatio).toBeGreaterThanOrEqual(4.5);

      const inlineRatio = getContrastRatio(codeColors.light.inlineForeground, codeColors.light.inlineBackground);
      expect(inlineRatio).toBeGreaterThanOrEqual(4.5);

      const keywordRatio = getContrastRatio(codeColors.light.keyword, codeColors.light.background);
      expect(keywordRatio).toBeGreaterThanOrEqual(4.5);

      const stringRatio = getContrastRatio(codeColors.light.string, codeColors.light.background);
      expect(stringRatio).toBeGreaterThanOrEqual(4.5);

      const numberRatio = getContrastRatio(codeColors.light.number, codeColors.light.background);
      expect(numberRatio).toBeGreaterThanOrEqual(4.5);

      const functionRatio = getContrastRatio(codeColors.light.function, codeColors.light.background);
      expect(functionRatio).toBeGreaterThanOrEqual(4.5);

      const typeRatio = getContrastRatio(codeColors.light.type, codeColors.light.background);
      expect(typeRatio).toBeGreaterThanOrEqual(4.5);
    });

    it('should have sufficient contrast for dark theme code blocks', () => {
      const bgRatio = getContrastRatio(codeColors.dark.foreground, codeColors.dark.background);
      expect(bgRatio).toBeGreaterThanOrEqual(4.5);

      const inlineRatio = getContrastRatio(codeColors.dark.inlineForeground, codeColors.dark.inlineBackground);
      expect(inlineRatio).toBeGreaterThanOrEqual(4.5);

      const keywordRatio = getContrastRatio(codeColors.dark.keyword, codeColors.dark.background);
      expect(keywordRatio).toBeGreaterThanOrEqual(4.5);

      const stringRatio = getContrastRatio(codeColors.dark.string, codeColors.dark.background);
      expect(stringRatio).toBeGreaterThanOrEqual(4.5);

      const numberRatio = getContrastRatio(codeColors.dark.number, codeColors.dark.background);
      expect(numberRatio).toBeGreaterThanOrEqual(4.5);

      const functionRatio = getContrastRatio(codeColors.dark.function, codeColors.dark.background);
      expect(functionRatio).toBeGreaterThanOrEqual(4.5);

      const typeRatio = getContrastRatio(codeColors.dark.type, codeColors.dark.background);
      expect(typeRatio).toBeGreaterThanOrEqual(4.5);
    });
  });

  describe('Focus and Interactive States', () => {
    it('should have sufficient contrast for focus indicators', () => {
      // Focus ring should be visible against both light and dark backgrounds
      const focusColor = '#3b82f6';
      const lightRatio = getContrastRatio(focusColor, lightTheme.background);
      const darkRatio = getContrastRatio(focusColor, darkTheme.background);
      
      expect(lightRatio).toBeGreaterThanOrEqual(3.0); // Minimum for non-text elements
      expect(darkRatio).toBeGreaterThanOrEqual(3.0);
    });

    it('should have sufficient contrast for hover states', () => {
      // Hover states should maintain readability
      const hoverPrimary = '#2563eb'; // Slightly darker primary
      const lightRatio = getContrastRatio('#ffffff', hoverPrimary);
      
      expect(lightRatio).toBeGreaterThanOrEqual(4.5);
    });
  });
});

// Export utilities for manual testing
export const accessibilityUtils = {
  getContrastRatio,
  hexToRgb,
  getLuminance,
  lightTheme,
  darkTheme,
  codeColors,
  
  checkColorPair(foreground: string, background: string): { ratio: number; passes: boolean; level: string } {
    const ratio = getContrastRatio(foreground, background);
    const passesAA = ratio >= 4.5;
    const passesAAA = ratio >= 7.0;
    
    let level = 'Fail';
    if (passesAAA) level = 'AAA';
    else if (passesAA) level = 'AA';
    
    return {
      ratio: Math.round(ratio * 100) / 100,
      passes: passesAA,
      level
    };
  },
  
  auditColorScheme() {
    console.log('=== Accessibility Color Audit ===\n');
    
    console.log('Light Theme:');
    console.log('Main text:', this.checkColorPair(lightTheme.foreground, lightTheme.background));
    console.log('Muted text:', this.checkColorPair(lightTheme.mutedForeground, lightTheme.background));
    console.log('Primary:', this.checkColorPair(lightTheme.primaryForeground, lightTheme.primary));
    console.log('Success:', this.checkColorPair(lightTheme.successForeground, lightTheme.success));
    console.log('Warning:', this.checkColorPair(lightTheme.warningForeground, lightTheme.warning));
    console.log('Destructive:', this.checkColorPair(lightTheme.destructiveForeground, lightTheme.destructive));
    
    console.log('\nDark Theme:');
    console.log('Main text:', this.checkColorPair(darkTheme.foreground, darkTheme.background));
    console.log('Muted text:', this.checkColorPair(darkTheme.mutedForeground, darkTheme.background));
    console.log('Primary:', this.checkColorPair(darkTheme.primaryForeground, darkTheme.primary));
    console.log('Success:', this.checkColorPair(darkTheme.successForeground, darkTheme.success));
    console.log('Warning:', this.checkColorPair(darkTheme.warningForeground, darkTheme.warning));
    console.log('Destructive:', this.checkColorPair(darkTheme.destructiveForeground, darkTheme.destructive));
  }
};
