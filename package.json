{"name": "mdeditor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdxeditor/editor": "^3.35.0", "@mohtasham/md-to-docx": "^1.0.0", "@types/dompurify": "^3.0.5", "@types/katex": "^0.16.7", "@types/marked": "^5.0.2", "@types/puppeteer": "^5.4.7", "@types/react-syntax-highlighter": "^15.5.13", "allotment": "^1.20.3", "dompurify": "^3.2.6", "framework7": "^8.3.4", "framework7-react": "^8.3.4", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^2.5.2", "katex": "^0.16.22", "markdown-it": "^14.1.0", "markdown-it-abbr": "^2.0.0", "markdown-it-attrs": "^4.3.1", "markdown-it-container": "^4.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-ins": "^4.0.0", "markdown-it-kbd": "^3.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-task-lists": "^2.1.1", "markdown-pdf": "^11.0.0", "marked": "^15.0.12", "mermaid": "^11.7.0", "next": "15.3.3", "next-themes": "^0.4.6", "playwright": "^1.53.1", "puppeteer": "^24.10.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^8.0.6", "react-pdf": "^9.2.1", "react-scroll-sync": "^1.0.2", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cypress": "^14.4.0", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "tailwindcss": "^4", "typescript": "^5"}}