/**
 * Enhanced Markdown Styles
 * Comprehensive styling for advanced markdown features
 */

/* Import KaTeX CSS for math rendering */
@import 'katex/dist/katex.min.css';

/* Import Highlight.js CSS for syntax highlighting */
@import 'highlight.js/styles/github.css';

/* Math expressions with enhanced styling */
.math-display,
.katex-display {
  margin: 1.5rem 0;
  text-align: center;
  overflow-x: auto;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -webkit-overflow-scrolling: touch;
}

.dark .math-display,
.dark .katex-display {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.math-inline,
.katex-inline {
  display: inline-block;
  margin: 0 0.1rem;
  vertical-align: baseline;
}

.math-error {
  color: #dc3545;
  background: #f8d7da;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.9em;
  border: 1px solid #f5c6cb;
  display: inline-block;
}

.dark .math-error {
  color: #f87171;
  background: #7f1d1d;
  border-color: #991b1b;
}

/* KaTeX specific overrides */
.katex {
  font-size: 1.1em;
}

.katex-display .katex {
  font-size: 1.2em;
}

/* Ensure KaTeX fonts load properly */
.katex .katex-mathml {
  position: absolute;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 0;
  border: 0;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

/* Mobile math optimizations */
@media (max-width: 768px) {
  .math-display,
  .katex-display {
    margin: 1rem 0;
    padding: 0.75rem;
    font-size: 0.9em;
  }

  .katex {
    font-size: 1em;
  }

  .katex-display .katex {
    font-size: 1.1em;
  }
}

/* Print styles for math */
@media print {
  .math-display,
  .katex-display {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
    page-break-inside: avoid;
  }

  .math-error {
    background: #f0f0f0 !important;
    color: #000 !important;
    border-color: #000 !important;
  }
}

/* Enhanced Mermaid diagrams */
.mermaid-diagram {
  margin: 2rem 0;
  text-align: center;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  overflow-x: auto;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  -webkit-overflow-scrolling: touch;
}

.dark .mermaid-diagram {
  background: #1e293b;
  border-color: #334155;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.mermaid-rendered svg {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* Mermaid theme overrides for better visibility */
.mermaid-diagram .node rect,
.mermaid-diagram .node circle,
.mermaid-diagram .node ellipse,
.mermaid-diagram .node polygon {
  fill: #f8fafc !important;
  stroke: #334155 !important;
  stroke-width: 2px !important;
}

.dark .mermaid-diagram .node rect,
.dark .mermaid-diagram .node circle,
.dark .mermaid-diagram .node ellipse,
.dark .mermaid-diagram .node polygon {
  fill: #334155 !important;
  stroke: #94a3b8 !important;
}

.mermaid-diagram .node .label {
  color: #1e293b !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.dark .mermaid-diagram .node .label {
  color: #e2e8f0 !important;
}

.mermaid-diagram .edgePath .path {
  stroke: #475569 !important;
  stroke-width: 2px !important;
}

.dark .mermaid-diagram .edgePath .path {
  stroke: #94a3b8 !important;
}

.mermaid-diagram .edgeLabel {
  background-color: #ffffff !important;
  color: #334155 !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
  font-size: 12px !important;
}

.dark .mermaid-diagram .edgeLabel {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
  border-color: #334155 !important;
}

.diagram-error {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  padding: 1rem;
  border-radius: 0.5rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  text-align: left;
}

.dark .diagram-error {
  color: #f87171;
  background: #7f1d1d;
  border-color: #991b1b;
}

/* Custom containers */
.custom-container {
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-weight: 600;
  gap: 0.5rem;
}

.container-icon {
  font-size: 1.2em;
}

.container-title {
  flex: 1;
}

.container-content {
  padding: 1rem;
}

.custom-container.note {
  border-left: 4px solid #0ea5e9;
}

.custom-container.note .container-header {
  background: #e0f2fe;
  color: #0369a1;
}

.custom-container.note .container-content {
  background: #f0f9ff;
}

.custom-container.warning {
  border-left: 4px solid #f59e0b;
}

.custom-container.warning .container-header {
  background: #fef3c7;
  color: #d97706;
}

.custom-container.warning .container-content {
  background: #fffbeb;
}

.custom-container.info {
  border-left: 4px solid #06b6d4;
}

.custom-container.info .container-header {
  background: #cffafe;
  color: #0891b2;
}

.custom-container.info .container-content {
  background: #f0fdfa;
}

.custom-container.tip {
  border-left: 4px solid #10b981;
}

.custom-container.tip .container-header {
  background: #d1fae5;
  color: #059669;
}

.custom-container.tip .container-content {
  background: #ecfdf5;
}

.custom-container.danger {
  border-left: 4px solid #ef4444;
}

.custom-container.danger .container-header {
  background: #fee2e2;
  color: #dc2626;
}

.custom-container.danger .container-content {
  background: #fef2f2;
}

.custom-container.success {
  border-left: 4px solid #22c55e;
}

.custom-container.success .container-header {
  background: #dcfce7;
  color: #16a34a;
}

.custom-container.success .container-content {
  background: #f0fdf4;
}

/* Enhanced code blocks with improved syntax highlighting */
.enhanced-code-block {
  margin: 1.5rem 0;
  border-radius: 0.75rem;
  overflow: hidden;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.dark .enhanced-code-block {
  background: #1e293b;
  border-color: #334155;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #e2e8f0;
  border-bottom: 1px solid #cbd5e1;
}

.dark .code-header {
  background: #334155;
  border-bottom-color: #475569;
}

.code-language {
  font-size: 0.875rem;
  font-weight: 600;
  color: #475569;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .code-language {
  color: #94a3b8;
}

.code-actions {
  display: flex;
  gap: 0.5rem;
}

.copy-code-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.copy-code-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.dark .copy-code-btn {
  background: #1d4ed8;
}

.dark .copy-code-btn:hover {
  background: #1e40af;
}

.copy-icon {
  font-size: 0.875em;
}

.code-content {
  margin: 0;
  padding: 1.25rem;
  overflow-x: auto;
  background: #ffffff;
  color: #334155;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  -webkit-overflow-scrolling: touch;
}

.dark .code-content {
  background: #0f172a;
  color: #e2e8f0;
}

.code-line {
  display: block;
  position: relative;
  padding-left: 3.5rem;
  min-height: 1.5rem;
}

.code-line::before {
  content: attr(data-line);
  position: absolute;
  left: 0;
  width: 3rem;
  text-align: right;
  color: #94a3b8;
  font-size: 0.75rem;
  user-select: none;
  padding-right: 0.75rem;
}

.dark .code-line::before {
  color: #64748b;
}

/* Syntax highlighting colors - Light theme */
.hljs-keyword,
.hljs-selector-tag,
.hljs-built_in,
.hljs-name,
.hljs-tag {
  color: #7c3aed; /* Purple for keywords */
}

.hljs-string,
.hljs-attr,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-template-tag {
  color: #059669; /* Green for strings */
}

.hljs-comment,
.hljs-quote {
  color: #6b7280; /* Gray for comments */
  font-style: italic;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-attribute {
  color: #dc2626; /* Red for numbers */
}

.hljs-function,
.hljs-title {
  color: #2563eb; /* Blue for functions */
}

.hljs-type,
.hljs-class {
  color: #ea580c; /* Orange for types */
}

.hljs-operator,
.hljs-punctuation {
  color: #475569; /* Dark gray for operators */
}

/* Dark theme syntax highlighting */
.dark .hljs-keyword,
.dark .hljs-selector-tag,
.dark .hljs-built_in,
.dark .hljs-name,
.dark .hljs-tag {
  color: #a78bfa; /* Light purple for keywords */
}

.dark .hljs-string,
.dark .hljs-attr,
.dark .hljs-selector-attr,
.dark .hljs-selector-pseudo,
.dark .hljs-template-tag {
  color: #34d399; /* Light green for strings */
}

.dark .hljs-comment,
.dark .hljs-quote {
  color: #9ca3af; /* Light gray for comments */
  font-style: italic;
}

.dark .hljs-number,
.dark .hljs-literal,
.dark .hljs-variable,
.dark .hljs-template-variable,
.dark .hljs-attribute {
  color: #f87171; /* Light red for numbers */
}

.dark .hljs-function,
.dark .hljs-title {
  color: #60a5fa; /* Light blue for functions */
}

.dark .hljs-type,
.dark .hljs-class {
  color: #fb923c; /* Light orange for types */
}

.dark .hljs-operator,
.dark .hljs-punctuation {
  color: #94a3b8; /* Light gray for operators */
}

/* Enhanced task lists */
.task-list-checkbox {
  margin-right: 0.5rem;
  cursor: pointer;
}

.task-completed {
  accent-color: #22c55e;
}

.task-pending {
  accent-color: #6b7280;
}

.task-in-progress {
  accent-color: #f59e0b;
}

.task-cancelled {
  accent-color: #ef4444;
}

/* Enhanced tables with mobile optimization */
.table-wrapper {
  margin: 1.5rem 0;
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.enhanced-table {
  width: 100%;
  min-width: 500px; /* Ensure minimum width for readability */
  border-collapse: collapse;
  background: white;
  font-size: 0.875rem;
}

.table-header {
  background: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header-cell {
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
  min-width: 100px;
}

.table-cell {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  word-wrap: break-word;
  max-width: 200px;
}

.enhanced-table tr:hover {
  background: #f8f9fa;
}

/* Mobile table optimizations */
@media (max-width: 768px) {
  .table-wrapper {
    margin: 1rem 0;
    border-radius: 6px;
    font-size: 0.8rem;
  }

  .enhanced-table {
    min-width: 600px; /* Wider minimum for mobile */
    font-size: 0.8rem;
  }

  .table-header-cell,
  .table-cell {
    padding: 0.5rem;
    min-width: 80px;
  }

  .table-cell {
    max-width: 150px;
  }
}

/* Enhanced lists */
.enhanced-list {
  margin: 1rem 0;
  padding-left: 2rem;
}

.list-item {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* Enhanced blockquotes */
.enhanced-blockquote {
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  border-left: 4px solid #6c757d;
  background: #f8f9fa;
  font-style: italic;
  color: #495057;
}

/* Enhanced links */
.enhanced-link {
  color: #0d6efd;
  text-decoration: none;
  transition: color 0.2s;
}

.enhanced-link:hover {
  color: #0a58ca;
  text-decoration: underline;
}

.external-link-icon {
  font-size: 0.8em;
  opacity: 0.7;
  margin-left: 0.2rem;
}

/* Enhanced headers */
.enhanced-header {
  position: relative;
  margin: 2rem 0 1rem 0;
  font-weight: 600;
  line-height: 1.2;
}

.header-anchor {
  position: absolute;
  left: -1.5rem;
  opacity: 0;
  color: #6c757d;
  text-decoration: none;
  font-weight: normal;
  transition: opacity 0.2s;
}

.enhanced-header:hover .header-anchor {
  opacity: 1;
}

.header-1 { font-size: 2.5rem; color: #212529; }
.header-2 { font-size: 2rem; color: #343a40; }
.header-3 { font-size: 1.5rem; color: #495057; }
.header-4 { font-size: 1.25rem; color: #6c757d; }
.header-5 { font-size: 1.1rem; color: #6c757d; }
.header-6 { font-size: 1rem; color: #6c757d; }

/* Enhanced paragraphs */
.enhanced-paragraph {
  margin: 1rem 0;
  line-height: 1.6;
  color: #212529;
}

/* Text formatting */
.md-highlight {
  background: #fff3cd;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.md-inserted {
  background: #d1ecf1;
  text-decoration: none;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.md-subscript {
  font-size: 0.8em;
}

.md-superscript {
  font-size: 0.8em;
}

.md-kbd {
  background: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 3px;
  padding: 0.1rem 0.3rem;
  font-family: monospace;
  font-size: 0.9em;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Progress bars */
.progress-bar {
  position: relative;
  width: 100%;
  height: 1.5rem;
  background: #e9ecef;
  border-radius: 0.75rem;
  overflow: hidden;
  margin: 1rem 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0d6efd, #0a58ca);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Footnotes */
.footnote-ref {
  color: #0d6efd;
  text-decoration: none;
  font-weight: 600;
}

.footnote {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-left: 3px solid #6c757d;
  font-size: 0.9em;
}

.footnote-backref {
  color: #6c757d;
  text-decoration: none;
  margin-right: 0.5rem;
}

/* Definition lists */
.definition-list {
  margin: 1rem 0;
}

.definition-list dt {
  font-weight: 600;
  margin-top: 1rem;
}

.definition-list dd {
  margin-left: 2rem;
  margin-bottom: 0.5rem;
}

/* Responsive design with comprehensive mobile optimizations */
@media (max-width: 768px) {
  /* Typography adjustments */
  .enhanced-header {
    font-size: 1.5rem;
    margin: 1.5rem 0 1rem 0;
  }

  .header-1 { font-size: 2rem; }
  .header-2 { font-size: 1.75rem; }
  .header-3 { font-size: 1.5rem; }
  .header-4 { font-size: 1.25rem; }
  .header-5 { font-size: 1.1rem; }
  .header-6 { font-size: 1rem; }

  .header-anchor {
    display: none;
  }

  .enhanced-paragraph {
    font-size: 0.95rem;
    line-height: 1.7;
    margin: 0.75rem 0;
  }

  /* Code blocks mobile optimization */
  .enhanced-code-block {
    margin: 1rem 0;
    border-radius: 6px;
    font-size: 0.8rem;
  }

  .code-content {
    font-size: 0.75rem;
    padding: 0.75rem;
    line-height: 1.4;
  }

  .code-header {
    padding: 0.4rem 0.75rem;
  }

  .code-language {
    font-size: 0.75rem;
  }

  .copy-code-btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }

  /* Lists mobile optimization */
  .enhanced-list {
    margin: 0.75rem 0;
    padding-left: 1.5rem;
  }

  .list-item {
    margin: 0.4rem 0;
    line-height: 1.6;
  }

  /* Blockquotes mobile optimization */
  .enhanced-blockquote {
    margin: 1rem 0;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  /* Custom containers mobile optimization */
  .custom-container {
    margin: 1rem 0;
    border-radius: 6px;
  }

  .container-header {
    padding: 0.6rem 0.75rem;
    font-size: 0.9rem;
  }

  .container-content {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .container-icon {
    font-size: 1.1em;
  }

  /* Math expressions mobile optimization */
  .math-display {
    margin: 1rem 0;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    overflow-x: auto;
  }

  .math-inline {
    font-size: 0.9rem;
  }

  /* Mermaid diagrams mobile optimization */
  .mermaid-diagram {
    margin: 1.5rem 0;
    padding: 0.75rem;
    border-radius: 6px;
    overflow-x: auto;
  }

  .mermaid-rendered svg {
    max-width: 100%;
    height: auto;
    min-width: 300px;
  }

  /* Progress bars mobile optimization */
  .progress-bar {
    height: 1.25rem;
    margin: 0.75rem 0;
    border-radius: 0.625rem;
  }

  .progress-text {
    font-size: 0.7rem;
  }

  /* Footnotes mobile optimization */
  .footnote {
    margin: 0.4rem 0;
    padding: 0.4rem;
    font-size: 0.85rem;
  }

  /* Definition lists mobile optimization */
  .definition-list dt {
    margin-top: 0.75rem;
    font-size: 0.9rem;
  }

  .definition-list dd {
    margin-left: 1.5rem;
    margin-bottom: 0.4rem;
    font-size: 0.9rem;
  }
}

/* Ultra-small mobile screens */
@media (max-width: 480px) {
  .enhanced-header {
    font-size: 1.25rem;
  }

  .header-1 { font-size: 1.75rem; }
  .header-2 { font-size: 1.5rem; }
  .header-3 { font-size: 1.25rem; }

  .enhanced-paragraph {
    font-size: 0.9rem;
  }

  .code-content {
    font-size: 0.7rem;
    padding: 0.5rem;
  }

  .enhanced-list {
    padding-left: 1.25rem;
  }

  .custom-container {
    margin: 0.75rem 0;
  }

  .container-header {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  .container-content {
    padding: 0.5rem;
    font-size: 0.85rem;
  }
}
